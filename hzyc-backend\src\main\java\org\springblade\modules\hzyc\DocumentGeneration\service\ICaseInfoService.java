package org.springblade.modules.hzyc.DocumentGeneration.service;

import cn.hutool.json.JSONArray;
import java.util.Map;

public interface ICaseInfoService {
    /**
     * 获取案件基本信息
     *
     * @param query 查询参数
     * @return 案件信息列表
     */
    JSONArray getCaseBasicInfo(Map<String, Object> query);

	JSONArray getAPIData(Map<String, Object> query, String serviceId);

    /**
     * 获取惠州案件抽样取证物品清单
     *
     * @param query 查询参数
     * @return 物品清单列表
     */
    JSONArray getCaseSamplingItemList(Map<String, Object> query);

    /**
     * 获取惠州案件处理审批日报
     */
    JSONArray getCaseHandleApprovalDailyReport(Map<String, Object> query);

    /**
     * 获取惠州案件调查终结报告日报
     */
    JSONArray getCaseInvestigationEndDailyReport(Map<String, Object> query);

    /**
     * 获取惠州案件询问笔录日报
     */
    JSONArray getCaseInquiryRecordDailyReport(Map<String, Object> query);

    /**
     * 获取惠州案件证据复制（提取）单日报
     */
    JSONArray getCaseEvidenceCopyDailyReport(Map<String, Object> query);

    /**
     * 获取惠州涉案物品信息日报
     */
    JSONArray getCaseItemInfoDailyReport(Map<String, Object> query);

    /**
     * 获取惠州行政处罚决定书日报
     */
    JSONArray getAdminPenaltyDecisionDailyReport(Map<String, Object> query);

    /**
     * 获取惠州行政处罚事先告知书信息日报
     */
    JSONArray getAdminPenaltyNoticeDailyReport(Map<String, Object> query);

    /**
     * 获取惠州证据先行登记保存通知书信息日报
     */
    JSONArray getEvidencePreservationNoticeDailyReport(Map<String, Object> query);

    /**
     * 获取惠州专卖案件送达回证日报
     */
    JSONArray getCaseDeliveryReceiptDailyReport(Map<String, Object> query);

    /**
     * 获取惠州听证告知书信息日报
     */
    JSONArray getHearingNoticeDailyReport(Map<String, Object> query);

    /**
     * 获取惠州询问通知书信息日报
     */
    JSONArray getInquiryNoticeDailyReport(Map<String, Object> query);

    /**
     * 获取惠州鉴别检验留样告知书信息日报
     */
    JSONArray getInspectionSampleNoticeDailyReport(Map<String, Object> query);

    /**
     * 获取惠州案件集体讨论记录日报
     */
    JSONArray getCaseDiscussionRecordDailyReport(Map<String, Object> query);
}

