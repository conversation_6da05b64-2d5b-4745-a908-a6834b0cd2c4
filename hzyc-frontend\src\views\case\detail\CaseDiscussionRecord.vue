<template>
  <div class="case-discussion-record-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>案件集体讨论记录</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.tobacco_bureau_name"
                placeholder="烟草专卖局名称"
                class="org-input"
              />
              烟草专卖局
            </div>

            <div class="document-title">
              <h2>案件集体讨论记录</h2>
            </div>
          </div>

          <!-- 基本信息表单 -->
          <div class="content-section">
            <div class="form-row">
              <span class="form-label">案由：</span>
              <el-input
                v-model="formData.case_number"
                placeholder="案件编号"
                class="form-input"
                style="width: 300px;"
              />
            </div>

            <div class="form-row">
              <span class="form-label">立案编号：</span>
              <el-input
                v-model="formData.case_number"
                placeholder="立案编号"
                class="form-input"
                style="width: 300px;"
              />
            </div>

            <div class="form-row">
              <span class="form-label">时间：</span>
              <el-input
                v-model="formData.start_year"
                placeholder="年"
                style="width: 60px;"
              />
              <span>年</span>
              <el-input
                v-model="formData.start_month"
                placeholder="月"
                style="width: 50px;"
              />
              <span>月</span>
              <el-input
                v-model="formData.start_day"
                placeholder="日"
                style="width: 50px;"
              />
              <span>日</span>
              <el-input
                v-model="formData.start_hour"
                placeholder="时"
                style="width: 50px;"
              />
              <span>时</span>
              <el-input
                v-model="formData.start_minute"
                placeholder="分"
                style="width: 50px;"
              />
              <span>分至</span>
              <el-input
                v-model="formData.end_hour"
                placeholder="时"
                style="width: 50px;"
              />
              <span>时</span>
              <el-input
                v-model="formData.end_minute"
                placeholder="分"
                style="width: 50px;"
              />
              <span>分</span>
            </div>

            <div class="form-row">
              <span class="form-label">地点：</span>
              <el-input
                v-model="formData.meeting_location"
                placeholder="会议地点"
                class="form-input"
                style="width: 300px;"
              />
            </div>

            <div class="form-row">
              <span class="form-label">主持人：</span>
              <el-input
                v-model="formData.moderator_name"
                placeholder="主持人姓名"
                style="width: 150px;"
              />
              <span style="margin-left: 20px;">职务：</span>
              <el-input
                v-model="formData.moderator_position"
                placeholder="职务"
                style="width: 120px;"
              />
            </div>

            <div class="form-row">
              <span class="form-label">出席人员姓名及职务：</span>
              <el-input
                v-model="formData.attendee_names_positions"
                placeholder="出席人员姓名及职务"
                class="form-input"
                style="width: 400px;"
              />
            </div>

            <div class="form-row">
              <span class="form-label">缺席人员姓名及职务：</span>
              <el-input
                v-model="formData.absent_names_positions"
                placeholder="缺席人员姓名及职务"
                class="form-input"
                style="width: 400px;"
              />
            </div>

            <div class="form-row">
              <span class="form-label">列席人员姓名及职务：</span>
              <el-input
                v-model="formData.observer_names_positions"
                placeholder="列席人员姓名及职务"
                class="form-input"
                style="width: 400px;"
              />
            </div>

            <div class="form-row">
              <span class="form-label">记录人：</span>
              <el-input
                v-model="formData.recorder_name"
                placeholder="记录人姓名"
                style="width: 150px;"
              />
              <span style="margin-left: 20px;">职务：</span>
              <el-input
                v-model="formData.recorder_position"
                placeholder="职务"
                style="width: 120px;"
              />
            </div>
          </div>

          <!-- 案件承办人员汇报 -->
          <div class="content-section">
            <div class="section-label">案件承办人员汇报案情及对案件的处理意见：</div>
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.case_handler_report"
                type="textarea"
                :autosize="{ minRows: 6 }"
                placeholder="案件承办人员汇报案情及对案件的处理意见"
                class="auto-resize-textarea"
                maxlength="2000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 讨论记录 -->
          <div class="content-section">
            <div class="section-label">讨论记录：</div>
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.discussion_record"
                type="textarea"
                :autosize="{ minRows: 8 }"
                placeholder="讨论记录"
                class="auto-resize-textarea"
                maxlength="3000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 结论性意见 -->
          <div class="content-section">
            <div class="section-label">结论性意见：</div>
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.conclusive_opinion"
                type="textarea"
                :autosize="{ minRows: 6 }"
                placeholder="结论性意见"
                class="auto-resize-textarea"
                maxlength="2000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 法制审核意见 -->
          <div class="content-section">
            <div class="section-label">法制审核意见：</div>
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.legal_review_opinion"
                type="textarea"
                :autosize="{ minRows: 4 }"
                placeholder="法制审核意见"
                class="auto-resize-textarea"
                maxlength="1500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 签名区域 -->
          <div class="signature-section">
            <div class="signature-line">
              <span>烟草专卖局负责人签署意见并签名：</span>
              <el-input
                v-model="formData.tobacco_bureau_name"
                placeholder="烟草专卖局名称"
                style="width: 200px;"
              />
            </div>
            <div class="signature-line">
              <span>列席人员签名：</span>
              <el-input
                v-model="formData.observer_signatures"
                placeholder="列席人员签名"
                style="width: 200px;"
              />
            </div>
          </div>
        </div>
      </template>

    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  tobacco_bureau_name: '',
  case_number: '',
  start_year: '',
  start_month: '',
  start_day: '',
  start_hour: '',
  start_minute: '',
  end_hour: '',
  end_minute: '',
  meeting_location: '',
  moderator_name: '',
  moderator_position: '',
  attendee_names_positions: '',
  absent_names_positions: '',
  observer_names_positions: '',
  recorder_name: '',
  recorder_position: '',
  case_handler_report: '',
  discussion_record: '',
  conclusive_opinion: '',
  legal_review_opinion: '',
  observer_signatures: ''
})

// 预览相关状态
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const isFullscreen = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      tobacco_bureau_name: docContent.tobacco_bureau_name || docContent.tobaccoBureauName || newVal.tobacco_bureau_name || '',
      case_number: docContent.case_number || docContent.caseNumber || newVal.case_number || '',
      start_year: docContent.start_year || docContent.startYear || newVal.start_year || '',
      start_month: docContent.start_month || docContent.startMonth || newVal.start_month || '',
      start_day: docContent.start_day || docContent.startDay || newVal.start_day || '',
      start_hour: docContent.start_hour || docContent.startHour || newVal.start_hour || '',
      start_minute: docContent.start_minute || docContent.startMinute || newVal.start_minute || '',
      end_hour: docContent.end_hour || docContent.endHour || newVal.end_hour || '',
      end_minute: docContent.end_minute || docContent.endMinute || newVal.end_minute || '',
      meeting_location: docContent.meeting_location || docContent.meetingLocation || newVal.meeting_location || '',
      moderator_name: docContent.moderator_name || docContent.moderatorName || newVal.moderator_name || '',
      moderator_position: docContent.moderator_position || docContent.moderatorPosition || newVal.moderator_position || '',
      attendee_names_positions: docContent.attendee_names_positions || docContent.attendeeNamesPositions || newVal.attendee_names_positions || '',
      absent_names_positions: docContent.absent_names_positions || docContent.absentNamesPositions || newVal.absent_names_positions || '',
      observer_names_positions: docContent.observer_names_positions || docContent.observerNamesPositions || newVal.observer_names_positions || '',
      recorder_name: docContent.recorder_name || docContent.recorderName || newVal.recorder_name || '',
      recorder_position: docContent.recorder_position || docContent.recorderPosition || newVal.recorder_position || '',
      case_handler_report: docContent.case_handler_report || docContent.caseHandlerReport || newVal.case_handler_report || '',
      discussion_record: docContent.discussion_record || docContent.discussionRecord || newVal.discussion_record || '',
      conclusive_opinion: docContent.conclusive_opinion || docContent.conclusiveOpinion || newVal.conclusive_opinion || '',
      legal_review_opinion: docContent.legal_review_opinion || docContent.legalReviewOpinion || newVal.legal_review_opinion || '',
      observer_signatures: docContent.observer_signatures || docContent.observerSignatures || newVal.observer_signatures || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    tobacco_bureau_name: formData.value.tobacco_bureau_name,
    case_number: formData.value.case_number,
    start_year: formData.value.start_year,
    start_month: formData.value.start_month,
    start_day: formData.value.start_day,
    start_hour: formData.value.start_hour,
    start_minute: formData.value.start_minute,
    end_hour: formData.value.end_hour,
    end_minute: formData.value.end_minute,
    meeting_location: formData.value.meeting_location,
    moderator_name: formData.value.moderator_name,
    moderator_position: formData.value.moderator_position,
    attendee_names_positions: formData.value.attendee_names_positions,
    absent_names_positions: formData.value.absent_names_positions,
    observer_names_positions: formData.value.observer_names_positions,
    recorder_name: formData.value.recorder_name,
    recorder_position: formData.value.recorder_position,
    case_handler_report: formData.value.case_handler_report,
    discussion_record: formData.value.discussion_record,
    conclusive_opinion: formData.value.conclusive_opinion,
    legal_review_opinion: formData.value.legal_review_opinion,
    observer_signatures: formData.value.observer_signatures
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '案件集体讨论记录'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'discussionRecord' || action === 'caseHandler' || action === 'legalReview') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */

.case-discussion-record-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.form-row {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  font-size: 14px;
}

.form-label {
  font-weight: bold;
  min-width: 120px;
  color: #303133;
}

.form-input {
  margin-right: 10px;
}

.section-label {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #303133;
}

.signature-line {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

/* 打印样式 */
@media print {
  .document-header {
    display: none;
  }

  .case-discussion-record-container {
    padding: 0;
    background: white;
  }
}
</style>
